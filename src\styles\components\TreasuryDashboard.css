.treasury-dashboard {
  background: linear-gradient(135deg, var(--bg-primary) 0%, #1f1f1f 50%, var(--bg-primary) 100%);
  padding: 80px 0;
  position: relative;
}

.treasury-dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(232, 90, 79, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 50%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 60px;
}

.dashboard-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.dashboard-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(232, 90, 79, 0.2);
  border-color: rgba(232, 90, 79, 0.3);
}

.stat-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  font-size: 14px;
  opacity: 0.7;
}

.stat-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.stat-change {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: #4ade80;
}

.stat-change.neutral {
  color: var(--text-secondary);
}

.change-icon {
  font-size: 12px;
}

/* Holdings Grid */
.holdings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

.holding-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.holding-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(232, 90, 79, 0.15);
  border-color: rgba(232, 90, 79, 0.3);
}

.holding-card:hover .holding-icon {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

.holding-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.holding-icon.btc {
  background: linear-gradient(135deg, #f7931a, #ffb347);
}

.holding-icon.sol {
  background: linear-gradient(135deg, #9945ff, #14f195);
}

.holding-icon.usdc {
  background: linear-gradient(135deg, #2775ca, #4dabf7);
}

.holding-icon.esvc {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
}

.holding-info {
  flex: 1;
}

.holding-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 4px;
}

.holding-amount {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.currency {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.holding-change {
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Dashboard Actions */
.dashboard-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.dashboard-actions .btn-primary,
.dashboard-actions .btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  padding: 16px 32px;
}

.arrow {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.btn-secondary:hover .arrow {
  transform: translateX(4px);
}

/* Responsive */
@media (max-width: 768px) {
  .treasury-dashboard {
    padding: 40px 0 !important; /* Reduced padding */
  }

  .dashboard-header {
    margin-bottom: 30px !important; /* Reduced margin */
  }

  .dashboard-title {
    font-size: 24px !important; /* Much smaller title */
    margin-bottom: 8px !important; /* Reduced margin */
  }

  .dashboard-subtitle {
    font-size: 14px !important; /* Smaller subtitle */
    line-height: 1.4 !important; /* Tighter line height */
    padding: 0 20px !important; /* Add horizontal padding */
    margin-bottom: 20px !important; /* Add space between subtitle and content */
  }

  .stats-grid,
  .holdings-grid {
    grid-template-columns: 1fr;
    gap: 16px !important; /* Increased gap for better spacing */
    margin-bottom: 30px !important; /* Reduced bottom margin */
    padding: 0 20px !important; /* Increased horizontal padding to match overview */
  }

  .stat-card,
  .holding-card {
    padding: 16px !important; /* Reduced card padding */
    border-radius: 8px !important; /* Smaller border radius */
  }

  .stat-label {
    font-size: 10px !important; /* Smaller labels */
    margin-bottom: 6px !important; /* Reduced margin */
  }

  .stat-value {
    font-size: 24px !important; /* Smaller values */
    margin-bottom: 6px !important; /* Reduced margin */
  }

  .stat-change {
    font-size: 12px !important; /* Smaller change indicators */
  }

  .holding-amount {
    font-size: 16px !important; /* Smaller holding amounts */
    margin-bottom: 2px !important; /* Reduced margin */
  }

  .holding-label {
    font-size: 10px !important; /* Smaller holding labels */
    margin-bottom: 2px !important; /* Reduced margin */
  }

  .holding-change {
    font-size: 10px !important; /* Smaller holding changes */
  }

  .currency {
    font-size: 12px !important; /* Smaller currency text */
  }

  .dashboard-actions {
    flex-direction: column;
    align-items: center;
    padding: 0 20px !important; /* Match other sections padding */
    gap: 16px !important; /* Increased gap for better spacing */
  }

  .dashboard-actions .btn-primary,
  .dashboard-actions .btn-secondary {
    width: 100%;
    max-width: 280px !important; /* Slightly smaller max width */
    justify-content: center;
    padding: 12px 24px !important; /* Reduced button padding */
    font-size: 14px !important; /* Smaller button text */
  }

  /* Additional mobile optimizations */
  .holding-icon {
    width: 32px !important; /* Smaller icons */
    height: 32px !important;
    font-size: 18px !important; /* Smaller icon text */
  }

  .holding-card {
    gap: 12px !important; /* Reduced gap between icon and content */
  }

  /* Ensure proper container spacing */
  .container {
    padding: 0 !important; /* Remove container padding on mobile */
  }

  /* Prevent content overflow */
  .treasury-dashboard {
    overflow-x: hidden !important;
  }
}
