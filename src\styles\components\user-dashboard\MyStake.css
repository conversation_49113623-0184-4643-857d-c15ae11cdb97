/* My Stake Content */
.my-stake-content {
  position: relative;
  z-index: 2;
}

/* Additional gradient blur under body */
.my-stake-content::before {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 400px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

.my-stake-content::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  right: 0px;
  top: 600px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* User Header */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 60px 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Desktop: Position controls outside greeting */
@media (min-width: 769px) {
  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-greeting {
    flex: 1;
    position: relative;
  }

  .header-controls {
    position: absolute;
    top: -20px !important;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
}

/* User Header Background - Override general layout */
.my-stake-container .user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 360px !important; /* Calculated to cover greeting + controls */
  background: #260D08;
  z-index: -1;
}

/* Mobile: Adjust red background height */
@media (max-width: 768px) {
  .my-stake-container .user-header::before {
    top: -120px;
    bottom: -300px; /* Extend down much further to cover all mobile content */
    height: 385px !important; /* Let bottom positioning control the height */
  }
}

/* Gradient Blur under greeting */
.user-header::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 200px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* User Greeting */
.user-greeting {
  position: relative;
}

.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
  font-weight: 400;
}

.header-controls {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

/* Balance Toggle */
.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-size: 14px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #404040;
  border-radius: 24px;
  transition: 0.3s;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: #FFFFFF;
  border-radius: 50%;
  transition: 0.3s;
}

.toggle-switch input:checked + .toggle-slider {
  background: #BF4129;
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* Stake Button */
.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 6px; /* Reduced gap */
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 6px; /* Smaller border radius */
  padding: 10px 20px !important; /* Consistent padding with UserOverview */
  font-family: 'Montserrat', sans-serif;
  font-size: 12px; /* Reduced font size */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-1px);
}

.btn-icon {
  width: 14px; /* Reduced icon size */
  height: 14px;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  margin-top: 10px; /* Reduced desktop spacing between red section and navbar */
}

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Wallet Selector Section */
.wallet-selector-section {
  width: 100%;
}

.wallet-selector-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  margin-bottom: 24px;
}

.wallet-dropdown {
  width: 100%;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  cursor: pointer;
  outline: none;
}

.wallet-dropdown:focus {
  border-color: #BF4129;
}

/* Stake Details Section */
.stake-details-section {
  width: 100%;
}

.stake-details-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: row;
  gap: 32px;
  align-items: flex-start;
}

/* Left Section - Amount Staked */
.stake-left-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 0 0 auto;
  min-width: 200px;
}

/* Right Section - ROI Progress */
.stake-right-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  flex: 1;
}

/* Stake Amount Section */
.stake-amount-section {
  text-align: left;
  margin-bottom: 24px;
}

.amount-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #CCCCCC;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.amount-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 4px;
}

.amount-unit {
  font-size: 24px;
  color: #CCCCCC;
  margin-left: 4px;
}

.amount-usd {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

/* ROI Progress Section */
.roi-progress-section {
  margin-bottom: 24px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: #4CAF50;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.progress-earned {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #4CAF50;
}

.progress-expected {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #CCCCCC;
}

.roi-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.roi-earned {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #4CAF50;
  font-weight: 600;
}

.roi-expected {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

/* Withdraw Section */
.withdraw-section {
  margin-bottom: 24px;
}

.withdraw-earned-btn {
  background: #c6741b;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.withdraw-earned-btn:hover {
  background: #b8661a;
  transform: translateY(-1px);
}

/* Stake Info Grid */
.stake-info-grid {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-top: 24px;
  background: transparent;
  border: none;
  padding: 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.info-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 600;
}

.status-active {
  color: #22C55E;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-header {
    padding: 80px 20px 20px;
    flex-direction: column;
    gap: 24px;
    text-align: left; /* Align to left instead of center */
  }

  .user-header::before {
    height: 480px; /* Extended further to fully contain header controls */
  }

  .user-greeting {
    width: 100%;
  }

  .header-controls {
    position: static;
    width: 100%;
    display: flex;
    justify-content: flex-start; /* Align to left */
    align-items: flex-start; /* Align to top */
    gap: 8px;
    flex-direction: column; /* Stack vertically */
    padding: 0;
    margin-left: 0; /* Remove left margin */
  }

  .balance-toggle {
    display: flex;
    justify-content: flex-start; /* Align to left instead of right */
    align-items: center;
    gap: 8px;
    min-width: auto;
  }

  .stake-esvc-btn {
    width: auto;
    min-width: 100px; /* Reduced minimum width */
    justify-content: center;
    padding: 10px 20px !important; /* Consistent mobile padding */
    font-size: 12px; /* Reduced mobile font size */
    margin: 0 0 16px 0 !important; /* Add space under button */
    align-self: flex-start; /* Align button to left */
  }

  .dashboard-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
    margin-top: -60px !important; /* Reduced negative margin to shift tabs down */
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .stake-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .stake-stats {
    justify-content: space-around;
    gap: 16px;
    flex-wrap: wrap;
  }

  /* Mobile: Create compact horizontal layout like the reference image */
  .stake-details-card {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    background: rgba(38, 38, 38, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0;
  }

  .stake-left-section {
    min-width: auto;
    order: 1;
  }

  .stake-right-section {
    order: 2;
  }

  /* Amount Staked Section - Mobile compact styling */
  .amount-label {
    font-size: 11px;
    color: #CCCCCC;
    text-transform: uppercase;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .amount-value {
    font-size: 24px;
    font-weight: 700;
    color: #FFFFFF;
    margin-bottom: 2px;
    line-height: 1.2;
  }

  .amount-unit {
    font-size: 18px;
    color: #CCCCCC;
    margin-left: 4px;
  }

  .amount-usd {
    font-size: 12px;
    color: #CCCCCC;
    margin-bottom: 16px;
    font-weight: 500;
  }

  /* Progress Bar Section - Mobile compact */
  .roi-progress-section {
    margin-bottom: 16px;
  }

  .progress-bar {
    height: 4px;
    background: #404040;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
  }

  .progress-fill {
    height: 100%;
    background: #4CAF50;
    border-radius: 2px;
  }

  .progress-labels {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
  }

  .progress-earned {
    font-size: 12px;
    color: #4CAF50;
    font-weight: 500;
  }

  .progress-expected {
    font-size: 12px;
    color: #CCCCCC;
    font-weight: 500;
  }

  /* Withdraw Button - Mobile compact */
  .withdraw-earned-btn {
    width: 100%;
    background: #c6741b;
    color: #FFFFFF;
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 16px;
  }

  /* Status Information Grid - Mobile compact */
  .stake-info-grid {
    display: flex;
    flex-direction: column;
    gap: 0;
    margin-top: 0;
    background: transparent;
    border: none;
    padding: 0;
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .info-row:last-child {
    border-bottom: none;
  }

  .info-label {
    font-size: 12px;
    color: #CCCCCC;
    font-weight: 500;
  }

  .info-value {
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 600;
  }

  .status-active {
    color: #4CAF50;
  }

  .greeting-text {
    font-size: 28px;
  }

  .greeting-subtitle {
    font-size: 16px;
  }

  .amount-value {
    font-size: 24px;
  }

  /* Wallet selector mobile styling */
  .wallet-selector-card {
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(38, 38, 38, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .wallet-dropdown {
    background: #404040;
    border: 1px solid #525252;
    color: #FFFFFF;
    font-size: 16px;
    padding: 12px 16px;
  }

  .withdraw-btn {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
    display: block;
  }

  /* Fix container widths */
  .user-transactions-container,
  .dashboard-layout > * {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .dashboard-content {
    max-width: 100%;
    width: 100%;
  }



  /* Move sidenav down */
  .user-sidenav-container {
    margin-top: 40px !important; /* Increased margin to push sidenav down */
    order: 1;
  }
}
