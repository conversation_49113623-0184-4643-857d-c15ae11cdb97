/* User Page Layout Container */
.user-page-layout-container {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background Blur Gradients */
.user-page-layout-container .blur-gradient {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.user-page-layout-container .blur-gradient-1 {
  width: 316px;
  height: 316px;
  left: -150px;
  top: -150px;
  background: #CC6754;
  opacity: 0.2;
  filter: blur(200px);
}

.user-page-layout-container .blur-gradient-2 {
  width: 239px;
  height: 239px;
  left: -50px;
  top: -50px;
  background: #D19049;
  opacity: 0.15;
  filter: blur(150px);
}

.user-page-layout-container .blur-gradient-3 {
  width: 400px;
  height: 400px;
  right: -200px;
  top: 200px;
  background: #BF4129;
  opacity: 0.1;
  filter: blur(250px);
}

.user-page-layout-container .blur-gradient-4 {
  width: 300px;
  height: 300px;
  left: 50%;
  top: 600px;
  transform: translateX(-50%);
  background: #F0C369;
  opacity: 0.08;
  filter: blur(200px);
}

.user-page-layout-container .blur-gradient-5 {
  width: 350px;
  height: 350px;
  right: -100px;
  bottom: 200px;
  background: #CC6754;
  opacity: 0.12;
  filter: blur(220px);
}

/* Main Content */
.user-page-main {
  padding-top: 120px;
  padding-bottom: 80px;
  min-height: calc(100vh - 120px);
  position: relative;
  z-index: 2;
}

/* User Page Content */
.user-page-content {
  position: relative;
  z-index: 2;
}

/* Additional gradient blur under body */
.user-page-content::before {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 400px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

.user-page-content::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  right: 0px;
  top: 600px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  margin: 0;
  font-family: 'Montserrat', sans-serif;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* User Header (Greeting Section) */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 60px 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Desktop: Position controls outside greeting */
@media (min-width: 769px) {
  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-greeting {
    flex: 1;
    position: relative;
  }

  .header-controls {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
}

/* User Header Background */
.user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 320px;
  background: #260D08;
  z-index: -1;
}

/* Gradient Blur under greeting */
.user-header::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 79px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

.greeting-text {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

.greeting-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 60px 0;
  font-family: 'Montserrat', sans-serif;
}

/* Page Body */
.page-body {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-page-layout-container .blur-gradient-1 {
    width: 200px;
    height: 200px;
    left: -80px;
    top: 60px;
  }

  .user-page-layout-container .blur-gradient-2 {
    width: 150px;
    height: 150px;
    right: -60px;
    top: 250px;
  }

  .user-page-layout-container .blur-gradient-3 {
    width: 180px;
    height: 180px;
    left: calc(50% - 180px/2 - 20px);
    top: 180px;
  }

  .user-page-layout-container .blur-gradient-4 {
    width: 180px;
    height: 180px;
    right: -30px;
    top: 220px;
  }

  .user-page-layout-container .blur-gradient-5 {
    width: 160px;
    height: 160px;
    left: calc(50% - 160px/2 + 80px);
    top: 400px;
  }

  .user-page-main {
    padding-top: 80px;
    padding-bottom: 60px;
  }

  .page-header {
    padding: 40px 20px 20px;
  }

  .page-title {
    font-size: 32px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .user-header {
    flex-direction: column;
    gap: 0;
    padding: 40px 20px 40px;
    align-items: stretch;
  }

  .user-header::before {
    height: 320px;
  }

  .user-greeting {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .greeting-text {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .greeting-subtitle {
    margin-bottom: 24px;
  }

  .header-controls {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 0;
    position: static;
  }

  .page-body {
    padding: 0 20px;
  }
}
