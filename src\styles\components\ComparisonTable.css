.comparison-table {
  background: var(--bg-primary);
  padding: 80px 0;
}

.comparison-header {
  text-align: center;
  margin-bottom: 60px;
}

.comparison-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.comparison-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
}

.comparison-content {
  max-width: 800px;
  margin: 0 auto 60px;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Headers */
.comparison-header-left,
.comparison-header-right {
  padding: 24px;
  text-align: center;
  font-size: 24px;
  font-weight: 700;
}

.comparison-header-left {
  background: var(--bg-card);
  color: var(--text-primary);
  border-right: 1px solid var(--border-color);
}

.comparison-header-right {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
  color: white;
}

/* Feature Items */
.feature-item {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
}

.feature-item:last-of-type {
  border-bottom: none;
}

.feature-item.traditional {
  background: var(--bg-card);
  color: var(--text-primary);
  border-right: 1px solid var(--border-color);
}

.feature-item.esvc {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

/* Action Buttons */
.comparison-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.comparison-actions .btn-primary,
.comparison-actions .btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  padding: 16px 32px;
}

/* Responsive */
@media (max-width: 768px) {
  .comparison-title {
    font-size: 36px;
  }
  
  .comparison-grid {
    grid-template-columns: 1fr;
  }
  
  .comparison-header-left {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .feature-item.traditional {
    border-right: none;
  }
  
  .comparison-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .comparison-actions .btn-primary,
  .comparison-actions .btn-secondary {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}
