import React from 'react';
import '../styles/components/ComparisonTable.css';

const ComparisonTable: React.FC = () => {
  const features = [
    {
      traditional: 'Speculative Trading',
      esvc: 'Token-gated startup funding'
    },
    {
      traditional: 'No revenue backing',
      esvc: 'Backed by real assets'
    },
    {
      traditional: 'Opaque Tokenomics',
      esvc: 'Transparent treasury'
    },
    {
      traditional: 'Low staking rewards',
      esvc: 'High-yield ESVC staking'
    }
  ];

  return (
    <section className="comparison-table">
      <div className="container">
        <div className="comparison-header">
          <h2 className="comparison-title">What Makes Us Different</h2>
          <p className="comparison-subtitle">ESVC Staking Versus Traditional Crypto</p>
        </div>

        <div className="comparison-content">
          <div className="comparison-grid">
            {/* Headers */}
            <div className="comparison-header-left">
              <h3>Traditional Crypto</h3>
            </div>
            <div className="comparison-header-right">
              <h3>ESVC</h3>
            </div>

            {/* Features */}
            {features.map((feature, index) => (
              <React.Fragment key={index}>
                <div className="feature-item traditional">
                  <span className="feature-icon">💰</span>
                  <span className="feature-text">{feature.traditional}</span>
                </div>
                <div className="feature-item esvc">
                  <span className="feature-icon">💰</span>
                  <span className="feature-text">{feature.esvc}</span>
                </div>
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="comparison-actions">
          <button className="btn-primary">
            Start Staking Now
            <span className="cta-icon">🚀</span>
          </button>
          <button className="btn-secondary">
            See How it Works
          </button>
        </div>
      </div>
    </section>
  );
};

export default ComparisonTable;
